# 虚拟顺序状态实现说明

## 工作原理

### 初始状态
```
原始规则数组: [RuleA, RuleB, RuleC]
规则ID:      ["0", "1", "2"]
虚拟顺序:    ["0", "1", "2"]
显示顺序:    [RuleA, RuleB, RuleC]
```

### 拖拽重排序后（将RuleA拖到最后）
```
原始规则数组: [RuleA, RuleB, RuleC]  // 不变
规则ID:      ["0", "1", "2"]         // 不变
虚拟顺序:    ["1", "2", "0"]         // 只修改这个
显示顺序:    [RuleB, RuleC, RuleA]   // 按虚拟顺序显示
```

### 编辑规则
- 点击显示位置0的规则（RuleB）
- 虚拟顺序[0] = "1"
- 传递 ruleIndex = "1" 给编辑页面
- 编辑页面正确获取到 RuleB

### 保存时
- 按虚拟顺序 ["1", "2", "0"] 重新排列
- 保存后原始数组变为: [RuleB, RuleC, RuleA]
- 重新加载时虚拟顺序重置为: ["0", "1", "2"]
- 显示顺序: [RuleB, RuleC, RuleA]

## 优势

1. **简单可靠**：使用数组索引作为ID，避免了复杂的ID生成和管理
2. **编辑正确**：编辑时能够获取到正确的原始索引
3. **性能好**：只修改虚拟顺序数组，不需要移动大量数据
4. **状态清晰**：虚拟顺序和原始数据分离，逻辑清晰

## 关键函数

- `handleReorder`: 只修改虚拟顺序
- `handleEditRule`: 通过虚拟索引找到原始索引
- `getOrderedRules`: 按虚拟顺序返回规则数组
- `handleSave`: 按虚拟顺序重新排列并保存
